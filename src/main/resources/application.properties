server.servlet.context-path=/api

spring.application.name=novelScrapper
spring.datasource.url=*********************************************************************************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres

# PostgreSQL specific configurations to handle LOBs
spring.datasource.hikari.auto-commit=false
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.properties.hibernate.connection.handling_mode=delayed_acquisition_and_release_after_transaction

spring.jpa.hibernate.ddl-auto=create
#spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Selenium Configuration
selenium.headless=true
selenium.timeout=30
selenium.pool.max-size=5

# Target domains that we want to stay on (comma-separated)
selenium.target-domains=novelbin.com
# URLs that require headful mode (comma-separated patterns, * for wildcard)
selenium.headful-patterns=https://novelbin.com/*,*captcha*,*recaptcha*,*verify*