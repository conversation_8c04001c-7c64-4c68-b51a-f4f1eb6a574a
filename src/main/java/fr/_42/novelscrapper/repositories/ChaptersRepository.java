package fr._42.novelscrapper.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import fr._42.novelscrapper.models.Chapter;

import java.net.ContentHandler;
import java.util.List;
import java.util.Optional;

@Repository
public interface ChaptersRepository extends JpaRepository<Chapter, Long> {

    boolean existsByChapterNumber(Long chapterNumber);

    List<Chapter> findAllByChapterNumberBetween(Long from, Long to);

    Optional<Chapter> findByChapterNumber(Long chapterNumber);
}

