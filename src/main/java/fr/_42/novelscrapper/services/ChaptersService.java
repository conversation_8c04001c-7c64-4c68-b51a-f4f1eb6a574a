package fr._42.novelscrapper.services;


import fr._42.novelscrapper.models.Chapter;
import fr._42.novelscrapper.repositories.ChaptersRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChaptersService {
    private final ChaptersRepository chaptersRepository;


    public ChaptersService(ChaptersRepository chaptersRepository) {
        this.chaptersRepository = chaptersRepository;
    }

    public void saveContent(Chapter chapter) {
        chaptersRepository.save(chapter);
    }

    public List<Chapter> saveChapters(List<Chapter> chapters) {
        // save only the chapters that are not already in the database by chapterNumber
        return chaptersRepository.saveAll(chapters.stream().filter(chapter -> !chaptersRepository.existsByChapterNumber(chapter.getChapterNumber())).toList());
    }

    public List<Chapter> getChapters(Long from, Long to) {
        return chaptersRepository.findAllByChapterNumberBetween(from, to);
    }

    public String getChapterByNumber(Long chapterNumber) {
        return chaptersRepository.findByChapterNumber(chapterNumber)
                .orElseThrow(() -> new IllegalArgumentException("Chapter not found"))
                .getContent();
    }
}
