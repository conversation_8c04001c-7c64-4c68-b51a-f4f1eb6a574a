package fr._42.novelscrapper.services;

import fr._42.novelscrapper.models.Chapter;
import io.github.bonigarcia.wdm.WebDriverManager;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import jakarta.annotation.PreDestroy;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class SeleniumService {
    private final WebDriver mainDriver; // Keep the main driver for single operations
    private final Logger logger = LoggerFactory.getLogger(SeleniumService.class.getName());
    private final ChaptersService chaptersService;

    // WebDriver Pool Configuration
    private final BlockingQueue<WebDriver> driverPool = new LinkedBlockingQueue<>();
    private final AtomicInteger poolSize = new AtomicInteger(0);
    private final int maxPoolSize;
    private final boolean headless;
    private final int timeoutSeconds;

    // Dynamic headless/headful configuration
    private final List<String> headfulUrlPatterns;

    public SeleniumService(WebDriver driver, ChaptersService chaptersService,
                          @Value("${selenium.pool.max-size:3}") int maxPoolSize,
                          @Value("${selenium.headless:true}") boolean headless,
                          @Value("${selenium.timeout:30}") int timeoutSeconds,
                          @Value("${selenium.headful-patterns:https://novelbin.com/*,*captcha*,*recaptcha*}") String headfulPatterns) {
        this.mainDriver = driver;
        this.chaptersService = chaptersService;
        this.maxPoolSize = maxPoolSize;
        this.headless = headless;
        this.timeoutSeconds = timeoutSeconds;
        this.headfulUrlPatterns = Arrays.asList(headfulPatterns.split(","));
        logger.info("SeleniumService initialized with max pool size: {}", maxPoolSize);
        logger.info("Headful URL patterns: {}", headfulUrlPatterns);
    }

    /**
     * Creates a new WebDriver instance for the pool
     */
    private WebDriver createWebDriver() {
        return createWebDriver(headless);
    }

    /**
     * Creates a new WebDriver instance with specified headless mode
     */
    private WebDriver createWebDriver(boolean isHeadless) {
        try {
            WebDriverManager.chromedriver().setup();
        } catch (Exception e) {
            System.setProperty("webdriver.chrome.driver", "chromedriver");
        }

        ChromeOptions options = new ChromeOptions();
        options.setBinary("/opt/google/chrome/google-chrome");

        if (isHeadless) {
            options.addArguments("--headless=new");
            logger.debug("Creating headless WebDriver");
        } else {
            logger.info("Creating headful WebDriver for manual interaction");
        }

        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--disable-gpu");
        options.addArguments("--disable-extensions");
        options.addArguments("--disable-web-security");
        options.addArguments("--allow-running-insecure-content");
        options.addArguments("--ignore-certificate-errors");
        options.addArguments("--ignore-ssl-errors");
        options.addArguments("--ignore-certificate-errors-spki-list");
        options.addArguments("--disable-features=VizDisplayCompositor");
        options.addArguments("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36");
        options.addArguments("--window-size=1920,1080");
        options.addArguments("--disable-background-timer-throttling");
        options.addArguments("--disable-backgrounding-occluded-windows");
        options.addArguments("--disable-renderer-backgrounding");

        WebDriver driver = new ChromeDriver(options);
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(timeoutSeconds));
        driver.manage().timeouts().pageLoadTimeout(Duration.ofSeconds(timeoutSeconds));
        driver.manage().timeouts().scriptTimeout(Duration.ofSeconds(timeoutSeconds));

        return driver;
    }

    /**
     * Checks if URL matches patterns that require headful mode
     */
    private boolean shouldUseHeadfulMode(String url) {
        return headfulUrlPatterns.stream()
                .anyMatch(pattern -> matchesPattern(url, pattern));
    }

    /**
     * Simple pattern matching (supports * wildcard)
     */
    private boolean matchesPattern(String url, String pattern) {
        String regex = pattern.replace("*", ".*");
        return url.matches(regex);
    }

    /**
     * Borrows a WebDriver from the pool or creates a new one if pool is not at capacity
     */
    private WebDriver borrowDriver() throws InterruptedException {
        WebDriver driver = driverPool.poll();
        if (driver == null && poolSize.get() < maxPoolSize) {
            driver = createWebDriver();
            poolSize.incrementAndGet();
            logger.info("Created new WebDriver. Pool size: {}", poolSize.get());
        } else if (driver == null) {
            // Wait for an available driver if pool is at max capacity
            logger.info("Waiting for available WebDriver from pool...");
            driver = driverPool.take();
        }
        return driver;
    }

    /**
     * Returns a WebDriver to the pool
     */
    private void returnDriver(WebDriver driver) {
        if (driver != null) {
            try {
                // Check if driver is still valid
                driver.getCurrentUrl();
                driverPool.offer(driver);
            } catch (Exception e) {
                // Driver is invalid, close it and decrement pool size
                logger.warn("Closing invalid WebDriver: {}", e.getMessage());
                try {
                    driver.quit();
                } catch (Exception ex) {
                    logger.error("Error closing invalid driver: {}", ex.getMessage());
                }
                poolSize.decrementAndGet();
            }
        }
    }

    /**
     * Smart navigation that detects redirects and switches to headful mode when needed
     */
    private void smartNavigate(WebDriver driver, String targetUrl) {
        try {
            logger.info("Smart navigating to: {}", targetUrl);

            // First, try normal navigation
            driver.get(targetUrl);

            // Wait a moment for any redirects to happen
            Thread.sleep(2000);

            String currentUrl = driver.getCurrentUrl();
            String targetDomain = extractDomain(targetUrl);
            String currentDomain = extractDomain(currentUrl);

            // Check if we've been redirected away from the target site
            if (!isOnTargetSite(currentUrl, targetDomain)) {
                logger.info("Detected redirect from {} to {}. Target domain: {}, Current domain: {}",
                           targetUrl, currentUrl, targetDomain, currentDomain);

                // We've been redirected (likely to CAPTCHA), switch to headful mode
                handleRedirectWithHeadfulMode(driver, targetUrl, currentUrl);
            } else {
                logger.info("Successfully navigated to target site: {}", currentUrl);
            }

        } catch (Exception e) {
            logger.error("Error in smart navigation to {}: {}", targetUrl, e.getMessage());
            // Fallback to normal navigation
            driver.get(targetUrl);
        }
    }

    /**
     * Handle redirect by switching to headful mode and waiting for user to return to target site
     */
    private void handleRedirectWithHeadfulMode(WebDriver driver, String targetUrl, String redirectUrl) {
        WebDriver headfulDriver = null;
        try {
            logger.info("=== REDIRECT DETECTED - SWITCHING TO HEADFUL MODE ===");
            logger.info("Target URL: {}", targetUrl);
            logger.info("Redirected to: {}", redirectUrl);

            String targetDomain = extractDomain(targetUrl);

            // Create headful driver for manual interaction
            headfulDriver = createWebDriver(false);

            // Transfer session to headful driver
            transferSession(driver, headfulDriver);

            // Navigate headful driver to the redirect URL
            headfulDriver.get(redirectUrl);

            logger.info("=== MANUAL INTERACTION REQUIRED ===");
            logger.info("Please complete the CAPTCHA or verification in the browser window");
            logger.info("Navigate back to {} when done", targetDomain);
            logger.info("The system will automatically detect when you return and continue...");

            // Wait for user to return to target site
            waitForReturnToTargetSite(headfulDriver, targetDomain);

            // Transfer session back to original driver
            transferSession(headfulDriver, driver);

            // Navigate original driver to the current URL from headful driver
            String finalUrl = headfulDriver.getCurrentUrl();
            logger.info("User returned to target site. Final URL: {}", finalUrl);

            driver.get(finalUrl);

        } catch (Exception e) {
            logger.error("Error handling redirect: {}", e.getMessage());
        } finally {
            if (headfulDriver != null) {
                try {
                    headfulDriver.quit();
                    logger.info("Closed headful browser window");
                } catch (Exception e) {
                    logger.error("Error closing headful driver: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * Extract domain from URL
     */
    private String extractDomain(String url) {
        try {
            if (url.contains("novelbin.com")) return "novelbin.com";
            if (url.contains("captcha")) return "captcha";
            if (url.contains("cloudflare")) return "cloudflare";

            // Generic domain extraction
            String domain = url.replaceAll("https?://", "").split("/")[0];
            return domain.toLowerCase();
        } catch (Exception e) {
            return url;
        }
    }

    /**
     * Check if current URL is on the target site
     */
    private boolean isOnTargetSite(String currentUrl, String targetDomain) {
        String currentDomain = extractDomain(currentUrl);
        return currentDomain.contains(targetDomain) || targetDomain.contains(currentDomain);
    }

    /**
     * Wait for user to return to target site
     */
    private void waitForReturnToTargetSite(WebDriver driver, String targetDomain) {
        try {
            long startTime = System.currentTimeMillis();
            long timeout = 600000; // 10 minutes timeout

            while (System.currentTimeMillis() - startTime < timeout) {
                String currentUrl = driver.getCurrentUrl();

                // Check if user has returned to target site
                if (isOnTargetSite(currentUrl, targetDomain)) {
                    logger.info("✅ User returned to target site: {}", currentUrl);

                    // Wait a bit more to ensure page is fully loaded
                    Thread.sleep(3000);
                    return;
                }

                // Log current status every 30 seconds
                if ((System.currentTimeMillis() - startTime) % 30000 < 2000) {
                    logger.info("⏳ Waiting for return to {}... Currently on: {}", targetDomain, extractDomain(currentUrl));
                }

                Thread.sleep(2000); // Check every 2 seconds
            }

            logger.warn("⚠️ Timeout waiting for return to target site. Continuing anyway...");

        } catch (Exception e) {
            logger.error("Error waiting for return to target site: {}", e.getMessage());
        }
    }

    /**
     * Check if driver is running in headless mode
     */
    private boolean isDriverHeadless(WebDriver driver) {
        try {
            // Try to get window size - headless drivers typically have fixed size
            return driver.manage().window().getSize().getWidth() == 1920 &&
                   driver.manage().window().getSize().getHeight() == 1080;
        } catch (Exception e) {
            return true; // Assume headless if we can't determine
        }
    }

    /**
     * Transfer cookies and session between drivers
     */
    private void transferSession(WebDriver fromDriver, WebDriver toDriver) {
        try {
            String currentUrl = fromDriver.getCurrentUrl();
            toDriver.get(currentUrl);

            // Transfer cookies
            fromDriver.manage().getCookies().forEach(cookie -> {
                try {
                    toDriver.manage().addCookie(cookie);
                } catch (Exception e) {
                    logger.debug("Could not transfer cookie: {}", cookie.getName());
                }
            });
        } catch (Exception e) {
            logger.warn("Could not transfer session: {}", e.getMessage());
        }
    }

    /**
     * Wait for user to complete manual interaction (CAPTCHA, etc.)
     */
    private void waitForUserInteraction(WebDriver driver, String originalUrl) {
        logger.info("=== MANUAL INTERACTION REQUIRED ===");
        logger.info("Please complete any required actions (CAPTCHA, etc.) in the browser window");
        logger.info("The process will continue automatically once you're redirected or after timeout");

        try {
            // Wait for URL to change (indicating user completed action)
            // or for specific elements that indicate success
            long startTime = System.currentTimeMillis();
            long timeout = 300000; // 5 minutes timeout

            while (System.currentTimeMillis() - startTime < timeout) {
                String currentUrl = driver.getCurrentUrl();

                // Check if URL changed significantly (not just query params)
                if (!isSameBasePage(originalUrl, currentUrl)) {
                    logger.info("URL changed to: {}. Continuing automation...", currentUrl);
                    break;
                }

                // Check for common success indicators
                if (isPageLoadedSuccessfully(driver)) {
                    logger.info("Page loaded successfully. Continuing automation...");
                    break;
                }

                Thread.sleep(2000); // Check every 2 seconds
            }
        } catch (Exception e) {
            logger.warn("Error during manual interaction wait: {}", e.getMessage());
        }
    }

    /**
     * Check if two URLs represent the same base page
     */
    private boolean isSameBasePage(String url1, String url2) {
        try {
            // Remove query parameters and fragments for comparison
            String base1 = url1.split("\\?")[0].split("#")[0];
            String base2 = url2.split("\\?")[0].split("#")[0];
            return base1.equals(base2);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Check if page has loaded successfully (no CAPTCHA blocking)
     */
    private boolean isPageLoadedSuccessfully(WebDriver driver) {
        try {
            // Check for common CAPTCHA indicators
            List<WebElement> captchaElements = driver.findElements(By.xpath(
                "//*[contains(@class,'captcha') or contains(@class,'recaptcha') or " +
                "contains(text(),'captcha') or contains(text(),'verify') or " +
                "contains(text(),'robot')]"
            ));

            // If no CAPTCHA elements found, consider it successful
            return captchaElements.isEmpty();
        } catch (Exception e) {
            return true; // Assume success if we can't check
        }
    }

    public String getTitles(String novelMainPage) {
        try {
            smartNavigate(mainDriver, novelMainPage);
            mainDriver.manage().timeouts().implicitlyWait(Duration.ofSeconds(5));
            List<WebElement> links = mainDriver.findElements(
                    By.xpath("//a[span[contains(@class,'chapter-title')]]")
            );
           if (links.isEmpty()) {
                logger.info("No titles found");
                return "SomethingWentWrong";
            }

            List<Chapter> chapters = links.stream().map(link -> {
                Matcher chapterNumberMatcher = Pattern.compile("Chapter (\\d+)").matcher(link.getText());
                Matcher chapterTitleMatcher = Pattern.compile("Chapter \\d+:(.*)").matcher(link.getText());
                Long chapterNumber = 0L;
                String title = "";
                if (chapterNumberMatcher.find())
                    chapterNumber = Long.parseLong(chapterNumberMatcher.group(1));
                if (chapterTitleMatcher.find())
                    title = chapterTitleMatcher.group(1);
                String url = link.getAttribute("href");
                return new Chapter(chapterNumber, title, url, null);
            }).toList();
            chaptersService.saveChapters(chapters);
        } catch (Exception e) {
            logger.error("Error: {}", e.getMessage());
        }
        return mainDriver.getPageSource();
    }

    public void scrapRange(Long from, Long to) {
        try {
            List<Chapter> chapters = chaptersService.getChapters(from, to);
            logger.info("Starting to scrape {} chapters from {} to {}", chapters.size(), from, to);

            // Process chapters in batches of 3
            processBatchedChapters(chapters, 3);
            logger.info("Completed scraping chapters from {} to {}", from, to);
        } catch (Exception e) {
            logger.error("Error in scrapRange: {}", e.getMessage());
        }
    }

    /**
     * Process chapters in batches to limit concurrent processing
     */
    private void processBatchedChapters(List<Chapter> chapters, int batchSize) {
        for (int i = 0; i < chapters.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, chapters.size());
            List<Chapter> batch = chapters.subList(i, endIndex);

            logger.info("Processing batch {}-{} of {} chapters", i + 1, endIndex, chapters.size());

            // Process current batch in parallel (max 3 at a time)
            batch.parallelStream().forEach(this::updateChapterContentWithPool);

            logger.info("Completed batch {}-{}", i + 1, endIndex);

            // Optional: Add a small delay between batches to be gentle on the server
            if (endIndex < chapters.size()) {
                try {
                    Thread.sleep(1000); // 1 second delay between batches
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.warn("Batch processing interrupted");
                    break;
                }
            }
        }
    }

    /**
     * Updates chapter content using a WebDriver from the pool
     * Frees the driver resource immediately after getting content
     */
    public void updateChapterContentWithPool(Chapter chapter) {
        WebDriver driver = null;
        try {
            driver = borrowDriver();
            logger.debug("Processing chapter {} with pooled driver", chapter.getChapterNumber());

            // Use smart navigation that handles redirects
            smartNavigate(driver, chapter.getUrl());

            // Verify we're on the correct page and can find content
            if (isOnCorrectChapterPage(driver, chapter)) {
                // Extract content and immediately free the driver
                String contentText = extractChapterContent(driver);

                // Free driver resource immediately after getting content
                returnDriver(driver);
                driver = null; // Set to null to prevent double return in finally block

                // Save content after freeing driver
                if (contentText != null && !contentText.trim().isEmpty()) {
                    chapter.setContent(contentText);
                    chaptersService.saveContent(chapter);
                    logger.info("✅ Saved content for chapter {} ({} characters)",
                               chapter.getChapterNumber(), contentText.length());
                } else {
                    logger.warn("⚠️ Empty content found for chapter {}", chapter.getChapterNumber());
                }

                logger.debug("Successfully processed chapter {} and freed driver", chapter.getChapterNumber());
            } else {
                logger.warn("Could not verify chapter page for chapter {}", chapter.getChapterNumber());
            }

        } catch (Exception e) {
            logger.error("Error processing chapter {}: {}", chapter.getChapterNumber(), e.getMessage());
        } finally {
            // Only return driver if it wasn't already returned
            if (driver != null) {
                returnDriver(driver);
            }
        }
    }

    /**
     * Check if we're on the correct chapter page
     */
    private boolean isOnCorrectChapterPage(WebDriver driver, Chapter chapter) {
        try {
            String currentUrl = driver.getCurrentUrl();

            // Check if we're on novelbin.com
            if (!currentUrl.contains("novelbin.com")) {
                logger.warn("Not on novelbin.com. Current URL: {}", currentUrl);
                return false;
            }

            // Check if content element exists
            List<WebElement> contentElements = driver.findElements(By.xpath("//div[contains(@class,'chr-c')]"));
            if (contentElements.isEmpty()) {
                logger.warn("Chapter content element not found on page: {}", currentUrl);
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.error("Error verifying chapter page: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Extract chapter content text only (without saving)
     * This allows immediate driver resource freeing
     */
    private String extractChapterContent(WebDriver driver) {
        try {
            driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(5));
            WebElement content = driver.findElement(By.xpath("//div[contains(@class,'chr-c')]"));
            return content.getText();
        } catch (Exception e) {
            logger.error("Error extracting chapter content: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Extract and save chapter content (legacy method for backward compatibility)
     */
    private void extractAndSaveChapterContent(WebDriver driver, Chapter chapter) {
        try {
            String contentText = extractChapterContent(driver);
            if (contentText != null && !contentText.trim().isEmpty()) {
                chapter.setContent(contentText);
                chaptersService.saveContent(chapter);
                logger.info("✅ Saved content for chapter {} ({} characters)",
                           chapter.getChapterNumber(), contentText.length());
            } else {
                logger.warn("⚠️ Empty content found for chapter {}", chapter.getChapterNumber());
            }
        } catch (Exception e) {
            logger.error("Error extracting and saving chapter content: {}", e.getMessage());
        }
    }

    /**
     * Legacy method for single chapter processing (uses main driver)
     */
    public void updateChapterContent(Chapter chapter) {
        try {
            smartNavigate(mainDriver, chapter.getUrl());
            mainDriver.manage().timeouts().implicitlyWait(Duration.ofSeconds(5));
            WebElement content = mainDriver.findElement(By.xpath("//div[contains(@class,'chr-c')]"));
//            chapter.setContent(content.getText());
            logger.info("chatper: {}",  content.getText().substring(0, 100));
//            chaptersService.saveContent(chapter);
        } catch (Exception e) {
            logger.error("Error updating chapter content: {}", e.getMessage());
        }
    }

    /**
     * Cleanup method to close all drivers in the pool
     */
    @PreDestroy
    public void cleanup() {
        logger.info("Cleaning up WebDriver pool...");
        while (!driverPool.isEmpty()) {
            WebDriver driver = driverPool.poll();
            if (driver != null) {
                try {
                    driver.quit();
                } catch (Exception e) {
                    logger.error("Error closing driver during cleanup: {}", e.getMessage());
                }
            }
        }
        logger.info("WebDriver pool cleanup completed. Pool size was: {}", poolSize.get());
    }

}
