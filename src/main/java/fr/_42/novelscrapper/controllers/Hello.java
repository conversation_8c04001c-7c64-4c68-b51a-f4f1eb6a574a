package fr._42.novelscrapper.controllers;

import fr._42.novelscrapper.services.ChaptersService;
import fr._42.novelscrapper.services.SeleniumService;
import org.apache.hc.client5.http.auth.ChallengeType;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import org.slf4j.Logger;

@Controller
public class Hello {
    private final SeleniumService seleniumService;
    private final ChaptersService chaptersService;
    private final Logger logger = LoggerFactory.getLogger(Hello.class.getName());

    public Hello(SeleniumService seleniumService, ChaptersService chaptersService) {
        this.seleniumService = seleniumService;
        this.chaptersService = chaptersService;
    }

    @GetMapping("/hello")
    @ResponseBody
    public String hello() {
        return seleniumService.getTitles("https://novelbin.com/b/childhood-friend-of-the-zenith#tab-chapters-title");
    }

    @GetMapping("/test-headful")
    @ResponseBody
    public String testHeadful() {
        return "Testing headful mode for novelbin.com - check console for browser window";
    }


    @GetMapping("/scrappage")
    @ResponseBody
    public String scrapPage() {
        seleniumService.scrapRange(400L, 420L);
        return "ok";
    }

    @GetMapping("/test")
    @ResponseBody
    public String test() {
        try {
            return chaptersService.getChapterByNumber(1L);
        } catch (IllegalArgumentException e) {
            logger.info("Chapter not found");
        }
        return "ko";
    }
}
