package fr._42.novelscrapper.config;

import io.github.bonigarcia.wdm.WebDriverManager;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
public class SeleniumConfig {

    @Value("${selenium.headless:true}")
    private boolean headless;

    @Value("${selenium.timeout:30}")
    private int timeoutSeconds;

    @Bean(destroyMethod = "quit")
    public WebDriver webDriver() {
        try {
            // Setup ChromeDriver automatically with version matching
            WebDriverManager.chromedriver()
                    .clearDriverCache()
                    .setup();
        } catch (Exception e) {
            // Fallback: try to use system ChromeDriver if WebDriverManager fails
            System.setProperty("webdriver.chrome.driver", "chromedriver");
        }

        ChromeOptions options = new ChromeOptions();

        // Set the correct Chrome binary path
        options.setBinary("/opt/google/chrome/google-chrome");

        // Essential options for stability
        if (headless) {
            options.addArguments("--headless=new");
        }
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--disable-gpu");
        options.addArguments("--disable-extensions");
        options.addArguments("--disable-web-security");
        options.addArguments("--allow-running-insecure-content");
        options.addArguments("--ignore-certificate-errors");
        options.addArguments("--ignore-ssl-errors");
        options.addArguments("--ignore-certificate-errors-spki-list");
        options.addArguments("--disable-features=VizDisplayCompositor");

        // User agent to avoid detection
        options.addArguments("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36");

        // Window size for consistent rendering
        options.addArguments("--window-size=1920,1080");

        // Performance optimizations
        options.addArguments("--disable-background-timer-throttling");
        options.addArguments("--disable-backgrounding-occluded-windows");
        options.addArguments("--disable-renderer-backgrounding");

        WebDriver driver = new ChromeDriver(options);

        // Set timeouts
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(timeoutSeconds));
        driver.manage().timeouts().pageLoadTimeout(Duration.ofSeconds(timeoutSeconds));
        driver.manage().timeouts().scriptTimeout(Duration.ofSeconds(timeoutSeconds));

        return driver;
    }
}
