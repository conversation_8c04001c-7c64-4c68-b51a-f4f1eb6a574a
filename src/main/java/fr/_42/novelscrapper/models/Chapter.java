package fr._42.novelscrapper.models;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Entity
@Table(name = "chapters")
@Getter
@Setter
public class Chapter {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long chapterId;

    @Column(nullable = false, name = "chapter_number")
    Long chapterNumber;

    @Column(nullable = false, name = "title")
    String title;

    @Column(nullable = false, name = "url")
    String url;

    @Column(nullable = true, columnDefinition = "TEXT")
    String content;

    public Chapter() {
    }

    public Chapter(Long chapterNumber, String title, String url, String content) {
        this.chapterNumber = chapterNumber;
        this.title = title;
        this.url = url;
        this.content = content;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        Chapter chapter = (Chapter) o;
        return Objects.equals(chapterId, chapter.chapterId) && Objects.equals(chapterNumber, chapter.chapterNumber) && Objects.equals(title, chapter.title) && Objects.equals(url, chapter.url) && Objects.equals(content, chapter.content);
    }

    @Override
    public int hashCode() {
        return Objects.hash(chapterId, chapterNumber, title, url, content);
    }

    @Override
    public String toString() {
        return "Chapter{" +
                "chapterId=" + chapterId +
                ", chapterNumber=" + chapterNumber +
                ", title='" + title + '\'' +
                ", url='" + url + '\'' +
                ", content='" + content + '\'' +
                '}';
    }
}
